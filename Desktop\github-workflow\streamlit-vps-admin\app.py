import streamlit as st
from dotenv import load_dotenv
import os
import json
import google.generativeai as genai
from ssh_runner import SS<PERSON><PERSON>unner

load_dotenv()

# Configuration for history management
MAX_HISTORY_LENGTH = int(os.environ.get("MAX_HISTORY_LENGTH", "5"))  # Number of message pairs to keep
KEEP_TERMINAL_OUTPUT_MESSAGES = int(os.environ.get("KEEP_TERMINAL_OUTPUT_MESSAGES", "1"))  # How many terminal outputs to keep


def get_step_number(msg_index):
    """Calculate step number based on position in conversation history"""
    step_count = 0
    for msg in st.session_state.conversation_history[:msg_index + 1]:
        if msg["role"] == "assistant":
            try:
                data = json.loads(msg["content"])
                if "command" in data or "input_request" in data:
                    step_count += 1
            except json.JSONDecodeError:
                continue
    return step_count


def validate_json_schema(data):
    """Validate that the JSON response follows one of the required schemas"""
    valid_schemas = [
        # Command Step
        {"description", "command", "requires_output", "danger_level"},
        # General Response
        {"response"},
        # User Input Request
        {"description", "input_request"},
        # User Choice
        {"description", "choices"},
        # Task Complete
        {"status", "message"},
        # Error (same as complete but different status)
        {"status", "message"}
    ]

    data_keys = set(data.keys())

    # Check if data matches any valid schema
    for schema in valid_schemas:
        if data_keys == schema:
            # Additional validation for specific fields
            if "danger_level" in data:
                if data["danger_level"] not in ["safe", "caution", "dangerous"]:
                    return False, f"Invalid danger_level: {data['danger_level']}"
            if "requires_output" in data:
                if not isinstance(data["requires_output"], bool):
                    return False, f"requires_output must be boolean, got: {type(data['requires_output'])}"
            if "status" in data:
                if data["status"] not in ["completed", "error"]:
                    return False, f"Invalid status: {data['status']}"
            return True, "Valid schema"

    return False, f"Invalid schema. Keys: {data_keys}. Must match one of the 6 required schemas."


def process_ai_response(conversation, selected_os):
    """Process AI response and handle conversation flow"""
    try:
        with st.spinner("AI is thinking..."):
            response = model.generate_content(conversation)
            raw_response = response.text.strip().replace("```json", "").replace("```", "").strip()

            # Parse JSON response
            data = json.loads(raw_response)

            # Validate JSON schema
            is_valid, validation_message = validate_json_schema(data)
            if not is_valid:
                print(f"=== INVALID JSON SCHEMA ===")
                print(f"Error: {validation_message}")
                print(f"Received: {json.dumps(data, indent=2)}")
                print("===========================")

                # Add feedback to conversation about the invalid schema
                feedback_msg = f"INVALID JSON SCHEMA: {validation_message}. You must use EXACTLY one of the 6 required schemas. Received: {json.dumps(data)}"
                st.session_state.conversation_history.append({
                    "role": "user",
                    "content": feedback_msg,
                    "hidden": True
                })

                # Create error response
                data = {
                    "status": "error",
                    "message": f"AI response format error: {validation_message}"
                }

            # Print JSON object to server console
            print("=== AI Response ===")
            print(json.dumps(data, indent=2))
            print("==================")

            st.session_state.conversation_history.append({"role": "assistant", "content": json.dumps(data)})

            # Check if task is complete
            if data.get("status") in ["completed", "error"]:
                st.session_state.task_active = False
                st.session_state.pending_choice_msg_index = None
                st.session_state.selected_choice = None

            st.rerun()

    except Exception as e:
        print(f"=== EXCEPTION IN AI RESPONSE ===")
        print(f"Error: {e}")
        print(f"Raw response: {raw_response if 'raw_response' in locals() else 'N/A'}")
        print("================================")
        st.session_state.conversation_history.append({"role": "assistant", "content": f"Error: {e}"})
        st.rerun()


def escape_html_for_llm(text):
    """
    Escape HTML angle brackets for LLM chat data to prevent parsing issues.
    Replaces < with &lt; and > with &gt;
    """
    if isinstance(text, str):
        return text.replace('<', '&lt;').replace('>', '&gt;')
    return text


def should_keep_in_history(msg):
    """
    Determine if a message should be kept in the LLM conversation history.

    Rules:
    1. Always keep the initial task message
    2. Always keep user input and LLM question/response pairs
    3. Remove response schemas and error schemas from history
    4. Keep terminal output only for the next message (n=1)
    """
    content = msg.get("content", "")

    # Always keep initial task messages
    if content.startswith("**New Task:**"):
        return True

    # Always keep user input and responses (non-hidden)
    if msg["role"] == "user" and not msg.get("hidden", False):
        return True

    # For assistant messages, check if it's a schema response
    if msg["role"] == "assistant":
        try:
            data = json.loads(content)
            # Keep command and status messages always
            if "command" in data or data.get("status") in ["completed", "error"]:
                return True

            # For response/input_request/choices schemas, we'll handle them in the main function
            # to keep only recent ones
            if "response" in data or "input_request" in data or "choices" in data:
                return "maybe"  # Special flag for conditional keeping

        except json.JSONDecodeError:
            # Keep non-JSON assistant messages
            return True

    return True


def build_smart_conversation(conversation_history, selected_os, max_history_length=None):
    """
    Build a smart conversation for the LLM with history management.

    Args:
        conversation_history: Full conversation history
        selected_os: Selected operating system
        max_history_length: Maximum number of message pairs to keep (uses config if None)

    Returns:
        Filtered conversation for LLM
    """
    if max_history_length is None:
        max_history_length = MAX_HISTORY_LENGTH
    if not conversation_history:
        return []

    # Find the initial task message
    initial_task_msg = None
    initial_task_index = -1
    for i, msg in enumerate(conversation_history):
        if msg["role"] == "user" and msg["content"].startswith("**New Task:**"):
            initial_task_msg = msg
            initial_task_index = i
            break

    if not initial_task_msg:
        return []

    # Start with the initial task prompt
    task_text = initial_task_msg["content"].replace("**New Task:** ", "")
    conversation = [{"role": "user", "parts": [get_initial_prompt(selected_os, task_text)]}]

    # Get messages after the initial task
    subsequent_messages = conversation_history[initial_task_index + 1:]

    # Filter messages based on smart rules
    filtered_messages = []

    for msg in subsequent_messages:
        if should_keep_in_history(msg):
            filtered_messages.append(msg)

    # Apply max history length (keep only the most recent N message pairs)
    if len(filtered_messages) > max_history_length * 2:  # *2 because each pair has user + assistant
        # Keep the most recent messages
        filtered_messages = filtered_messages[-(max_history_length * 2):]

    # Handle terminal output special case - keep only for next message (n=1)
    terminal_output_indices = []
    for i, msg in enumerate(filtered_messages):
        if msg.get("hidden", False) and msg["role"] == "user":
            # This is terminal output
            terminal_output_indices.append(i)

    # Keep only the most recent N terminal outputs based on configuration
    if len(terminal_output_indices) > KEEP_TERMINAL_OUTPUT_MESSAGES:
        # Remove all but the last N terminal outputs
        indices_to_remove = terminal_output_indices[:-KEEP_TERMINAL_OUTPUT_MESSAGES]
        filtered_messages = [msg for i, msg in enumerate(filtered_messages) if i not in indices_to_remove]

    # Convert filtered messages to conversation format
    for msg in filtered_messages:
        if msg["role"] == "user":
            escaped_content = escape_html_for_llm(msg["content"])
            conversation.append({"role": "user", "parts": [escaped_content]})
        else:
            conversation.append({"role": "model", "parts": [msg["content"]]})

    # Debug: Print conversation length to console
    print(f"=== Smart Conversation Built ===")
    print(f"Total original messages: {len(conversation_history)}")
    print(f"Filtered messages: {len(filtered_messages)}")
    print(f"Final conversation length: {len(conversation)}")
    print(f"Max history length: {max_history_length}")
    print(f"Keep terminal outputs: {KEEP_TERMINAL_OUTPUT_MESSAGES}")
    print("================================")

    return conversation

st.title("🤖 Servora - Server AI Assistant")

# OS selector and New Task button in same line
col1, col2 = st.columns([3, 1])
with col1:
    os_list = ["Ubuntu", "Debian", "CentOS", "RHEL", "Fedora", "Alpine", "Windows", "MacOS"]
    selected_os = st.selectbox("Target OS:", os_list)
with col2:
    st.markdown("<br>", unsafe_allow_html=True)  # Align with selectbox
    if st.button("New Task"):
        st.session_state.conversation_history = []
        st.session_state.task_active = False
        st.session_state.choice_made = False
        st.session_state.pending_choice_msg_index = None
        st.session_state.selected_choice = None
        st.session_state.executed_commands = set()
        st.session_state.processing_commands = set()
        # Clear all stored results
        keys_to_remove = [key for key in st.session_state.keys() if key.startswith("result_")]
        for key in keys_to_remove:
            del st.session_state[key]
        st.rerun()

# API setup
api_key = os.environ.get("GEMINI_API_KEY")
if not api_key:
    st.stop()

genai.configure(api_key=api_key)

# System instruction for the model
system_instruction = """# ROLE: Expert Server Administrator AI

You are `Servora`, an expert server administrator assistant that provides precise, step-by-step guidance for server administration tasks.

## CORE RULES:

1. **OS-SPECIFIC**: All commands must be 100% compatible with the target OS
2. **ONE STEP ONLY**: Provide exactly one command per response
3. **NON-INTERACTIVE**: Use `sed`, `awk`, `echo`, `heredoc` instead of `nano`, `vim`, `vi`, `emacs`, `crontab -e`, `visudo`
4. **JSON FORMAT**: Always respond with one of the 6 JSON schemas below
5. **SECURITY**: Use `sudo` for privileged commands, follow security best practices
6. **ADAPTIVE**: Use command output and user feedback to inform next steps
7. **CONVERSATIONAL**: Be helpful, detailed, and friendly. Use bullet points, examples, and comprehensive explanations when answering questions
8. **REQUEST INPUT**: Always ask for user-defined paths, filenames, or variables before using them

## TASK LOGIC:

- **Simple tasks** (status checks, listings): Provide command → Mark complete after execution
- **Complex tasks** (installations, configurations): Break into minimal essential steps
- **Always evaluate**: After each step, check if original request is satisfied
- **End when done**: Use completion JSON when task is fully accomplished
- **JSON SCHEMAS**: Always respond with **one** and only **one** of the following JSON schemas

## MANDATORY JSON SCHEMAS - USE EXACTLY ONE:

### 1. Command Step (for terminal commands):
```json
{
  "description": "What this step does and why",
  "command": "Single terminal command",
  "requires_output": true,
  "danger_level": "safe"
}
```

### 2. General Response (for questions/information):
```json
{
  "response": "Detailed, helpful answer with examples and bullet points. Be comprehensive and friendly."
}
```

### 3. User Input Request (when you need information):
```json
{
  "description": "Why this input is needed",
  "input_request": "Specific question for user"
}
```

### 4. User Choice (for multiple options):
```json
{
  "description": "Why choose from these options",
  "choices": ["Option A", "Option B", "Option C"]
}
```

### 5. Task Complete (when finished):
```json
{
  "status": "completed",
  "message": "Summary of accomplished work"
}
```

### 6. Error (when cannot proceed):
```json
{
  "status": "error",
  "message": "Why request cannot be fulfilled"
}
```

EXAMPLES OF CORRECT USAGE:

When user asks "what can you do?":
```json
{
  "response": "I can help you with various server administration tasks:\\n\\n- **Package Management**: Install, update, remove software packages\\n- **File Operations**: Create, copy, move, delete files and directories\\n- **Service Management**: Start, stop, restart system services\\n- **User Management**: Create, modify user accounts and permissions\\n- **Network Configuration**: Configure interfaces, firewall rules\\n- **System Monitoring**: Check disk space, memory usage, processes\\n- **Security**: Update system, configure SSH, manage certificates\\n\\nJust tell me what you'd like to accomplish!"
}
```

For dangerous operations:
```json
{
  "status": "error",
  "message": "Cannot remove root directory - this would destroy the system"
}
```

For needing user input:
```json
{
  "description": "Need directory path to proceed",
  "input_request": "Which directory do you want to remove?"
}
```

IMPORTANT:
- Be conversational, detailed, and helpful in your responses
- Use markdown formatting: **bold**, bullet points with `-`, line breaks with `\\n`
- When asked "what can you do?", provide a detailed list with examples
- Always use the correct JSON format but make the content rich and informative
- Responses will be rendered as markdown in the UI"""

model = genai.GenerativeModel(
    model_name=os.environ.get("GEMINI_MODEL"),
    system_instruction=system_instruction
)


def get_initial_prompt(os_type, request):
    """Generate task-specific prompt for the user request"""
    return f"**OS:** {os_type}\n**TASK:** {request}"


# Initialize session state
if "conversation_history" not in st.session_state:
    st.session_state.conversation_history = []
if "task_active" not in st.session_state:
    st.session_state.task_active = False
if "choice_made" not in st.session_state:
    st.session_state.choice_made = False
if "pending_choice_msg_index" not in st.session_state:
    st.session_state.pending_choice_msg_index = None
if "selected_choice" not in st.session_state:
    st.session_state.selected_choice = None
if "ssh_runner" not in st.session_state:
    st.session_state.ssh_runner = SSHRunner()
if "executed_commands" not in st.session_state:
    st.session_state.executed_commands = set()
if "processing_commands" not in st.session_state:
    st.session_state.processing_commands = set()

# Display chat messages
for msg_index, msg in enumerate(st.session_state.conversation_history):
    # Skip hidden messages (AI feedback from command execution)
    if msg.get("hidden", False):
        continue

    if msg["role"] == "user":
        with st.chat_message("user"):
            st.write(msg["content"])
    else:
        with st.chat_message("assistant"):
            # Check if this is a JSON message
            try:
                data = json.loads(msg["content"])
                is_json = True
            except:
                is_json = False
                data = None

            if is_json and data:
                # Skip displaying if this is a choice message that's being processed
                if ("choices" in data and
                    st.session_state.choice_made and
                    msg_index == st.session_state.pending_choice_msg_index):
                    # Don't display anything for choice messages being processed
                    pass
                elif data.get("status") == "completed":
                    st.success(f"✅ **Task Completed**\n\n{data.get('message')}")
                elif data.get("status") == "error":
                    st.error(f"❌ **Error**\n\n{data.get('message')}")
                elif "command" in data:
                    step_num = get_step_number(msg_index)
                    st.write(f"**Step {step_num}:** {data.get('description')}")
                    danger_color = {"safe": "🟢", "caution": "🟡", "dangerous": "🔴"}

                    # Command display with play button or result
                    command_key = f"cmd_{msg_index}"

                    if command_key in st.session_state.executed_commands:
                        # Show command and result
                        st.code(data["command"])

                        # Get stored result
                        result_key = f"result_{msg_index}"
                        if result_key in st.session_state:
                            result = st.session_state[result_key]

                            # Terminal-like display
                            st.markdown("**Terminal Output:**")
                            terminal_content = f"$ {data['command']}\n"

                            if result['stdout']:
                                terminal_content += result['stdout'] + "\n"

                            if result['stderr']:
                                terminal_content += f"stderr: {result['stderr']}\n"

                            terminal_content += f"Exit code: {result['exit_code']}"

                            st.code(terminal_content, language="bash")
                    elif command_key in st.session_state.processing_commands:
                        # Show command with processing message
                        st.code(data["command"])
                        st.info("🔄 Executing command...")
                    else:
                        # Show command with run button
                        col1, col2 = st.columns([6, 1])
                        with col1:
                            st.code(data["command"])
                        with col2:
                            button_clicked = st.button("Run ▶", key=f"run_cmd_{msg_index}", help="Run command",
                                                     disabled=command_key in st.session_state.processing_commands)

                        # Handle button click outside of columns for proper spinner display
                        if button_clicked and command_key not in st.session_state.processing_commands:
                            # Immediately mark as processing to prevent multiple clicks
                            st.session_state.processing_commands.add(command_key)
                            st.rerun()  # Force immediate UI update

                    # Handle command execution for processing commands
                    if command_key in st.session_state.processing_commands and command_key not in st.session_state.executed_commands:
                        # Execute command via SSH
                        result = st.session_state.ssh_runner.run_command(data["command"])

                        # Store result and mark as executed
                        st.session_state[f"result_{msg_index}"] = result
                        st.session_state.executed_commands.add(command_key)
                        st.session_state.processing_commands.discard(command_key)

                        # Prepare feedback message for AI (hidden from chat)
                        feedback_parts = [f"Done, exit_code:{result['exit_code']}"]

                        # Add stdout only if requires_output is true
                        if data.get("requires_output", False) and result['stdout']:
                            feedback_parts.append(f"stdout:{result['stdout']}")

                        # Always add stderr if it exists and not empty
                        if result['stderr']:
                            feedback_parts.append(f"stderr:{result['stderr']}")

                        feedback = "\n".join(feedback_parts)

                        # Add feedback to conversation (hidden from display)
                        st.session_state.conversation_history.append({
                            "role": "user",
                            "content": feedback,
                            "hidden": True  # Mark as hidden from display
                        })

                        # Build smart conversation for AI and get response
                        conversation = build_smart_conversation(st.session_state.conversation_history, selected_os)

                        # Process AI response
                        process_ai_response(conversation, selected_os)

                    st.write(
                        f"{danger_color.get(data.get('danger_level'), '❓')} **{data.get('danger_level', 'unknown').upper()}**")
                elif "input_request" in data:
                    step_num = get_step_number(msg_index)
                    st.write(f"**Step {step_num}:** {data.get('description')}")
                    st.info(f"**Input needed:** {data.get('input_request')}")
                elif "response" in data:
                    # Handle general conversation responses with markdown support
                    st.markdown(data["response"])
                elif "choices" in data:
                    # Handle user choice selection with buttons
                    st.write(data.get("description", "Please select from the following options:"))

                    # Check if this is the most recent choice message and no choice has been made yet
                    is_latest_choice = (msg_index == len(st.session_state.conversation_history) - 1 and
                                      st.session_state.task_active and
                                      not st.session_state.choice_made)

                    # Check if a choice was made for this message
                    choice_was_made = st.session_state.pending_choice_msg_index == msg_index

                    # Create buttons for choices
                    cols = st.columns(min(len(data["choices"]), 3))  # Max 3 columns
                    for i, choice in enumerate(data["choices"]):
                        col_index = i % len(cols)
                        with cols[col_index]:
                            button_key = f"choice_{msg_index}_{i}"

                            # Check if this choice was selected
                            is_selected = (choice_was_made and
                                         st.session_state.selected_choice == choice)

                            # Show selected choice differently
                            if is_selected:
                                st.button(
                                    f"✅ {choice}",
                                    key=f"{button_key}_selected",
                                    disabled=True,
                                    use_container_width=True
                                )
                            else:
                                if st.button(
                                    choice,
                                    key=button_key,
                                    disabled=not is_latest_choice or choice_was_made,
                                    use_container_width=True
                                ):
                                    if is_latest_choice and not choice_was_made:
                                        # Mark choice as made and store the selection
                                        st.session_state.choice_made = True
                                        st.session_state.pending_choice_msg_index = msg_index
                                        st.session_state.selected_choice = choice

                                        # Add user choice to conversation
                                        st.session_state.conversation_history.append({
                                            "role": "user",
                                            "content": choice
                                        })
                                        st.rerun()
                else:
                    # Unknown JSON format - don't display raw JSON
                    st.write("AI response received")
            else:
                # Non-JSON content - display as is
                st.write(msg["content"])

# Chat input
if not st.session_state.task_active:
    # New task input
    if task := st.chat_input("Enter your server administration task..."):
        # Add user message to history
        st.session_state.conversation_history.append({"role": "user", "content": f"**New Task:** {task}"})

        # Display user message immediately
        with st.chat_message("user"):
            st.write(f"**New Task:** {task}")

        # Create initial prompt and get AI response
        initial_prompt = get_initial_prompt(selected_os, task)

        try:
            with st.spinner("AI is thinking..."):
                response = model.generate_content(initial_prompt)
                raw_response = response.text.strip().replace("```json", "").replace("```", "").strip()

                # Parse JSON response
                data = json.loads(raw_response)

                # Print JSON object to server console
                print("=== AI Response (Initial Task) ===")
                print(json.dumps(data, indent=2))
                print("================================")

                st.session_state.conversation_history.append({"role": "assistant", "content": json.dumps(data)})

                # Check if task is complete or needs continuation
                if data.get("status") in ["completed", "error"]:
                    st.session_state.task_active = False
                else:
                    st.session_state.task_active = True

                st.rerun()

        except Exception as e:
            st.session_state.conversation_history.append({"role": "assistant", "content": f"Error: {e}"})
            st.rerun()

else:
    # Handle choice selection or feedback input for active task

    # Check if we need to process a choice that was just made
    if st.session_state.choice_made:
        # Reset choice state but keep the selection info
        st.session_state.choice_made = False

        # Build smart conversation for AI (choice was already added to history)
        conversation = build_smart_conversation(st.session_state.conversation_history, selected_os)

        # Process AI response
        process_ai_response(conversation, selected_os)

    # Regular feedback input
    elif feedback := st.chat_input("Enter command output or feedback..."):
        # Add user message to history
        st.session_state.conversation_history.append({"role": "user", "content": feedback})

        # Display user message immediately
        with st.chat_message("user"):
            st.write(feedback)

        # Build smart conversation for AI
        conversation = build_smart_conversation(st.session_state.conversation_history, selected_os)

        # Process AI response
        process_ai_response(conversation, selected_os)

# Run via: streamlit run app.py